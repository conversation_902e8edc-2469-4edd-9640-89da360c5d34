import React, { useState, useEffect } from 'react';
import { View, StyleSheet, FlatList, ActivityIndicator, TouchableOpacity, Image } from 'react-native';
import { Text } from 'react-native-paper';
import { TextInput } from 'react-native';
import { Winicon, hideBottomSheet } from 'wini-mobile-components';
import { ColorThemes } from '../assets/skin/colors';
import { TypoSkin } from '../assets/skin/typography';
import { DataController } from '../base/baseController';
import store from '../redux/store/store';
import ConfigAPI from '../Config/ConfigAPI';

interface Customer {
  Id: string;
  Name: string;
  Mobile?: string;
  Email?: string;
  AvatarUrl?: string;
}

interface CustomerBottomSheetProps {
  onSelectCustomer: (customer: Customer) => void;
  ref: any;
}

const CustomerBottomSheet: React.FC<CustomerBottomSheetProps> = ({ onSelectCustomer, ref }) => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const customerController = new DataController('Customer');
  const pageSize = 20;

  // Load customers when component mounts
  useEffect(() => {
    loadCustomers(true);
  }, []);

    const loadCustomers = async (isRefresh = false) => {
      if (loading || loadingMore) return;
      
      const currentPage = isRefresh ? 1 : page;
      
      if (isRefresh) {
        setLoading(true);
        setCustomers([]);
        setPage(1);
        setHasMore(true);
      } else {
        setLoadingMore(true);
      }

      try {
        let query = `@Status:[1] -@Id: {${store.getState().customer.data?.Id}}`; // Active customers only
        if (searchText.trim()) {
          query += ` (@Name:(*${searchText.trim()}*) | @Mobile:(*${searchText.trim()}*))`;
        }

        const response = await customerController.getListSimple({
          page: currentPage,
          size: pageSize,
          query: query,
          returns: ['Id', 'Name', 'Mobile', 'Email', 'AvatarUrl'],
          sortby: { BY: 'Name', DIRECTION: 'ASC' },
        });

        if (response.code === 200) {
          const newCustomers = response.data || [];
          
          if (isRefresh) {
            setCustomers(newCustomers);
          } else {
            setCustomers(prev => [...prev, ...newCustomers]);
          }
          
          setHasMore(newCustomers.length === pageSize);
          setPage(currentPage + 1);
        }
      } catch (error) {
        console.error('Error loading customers:', error);
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    };

    const handleSearch = (text: string) => {
      setSearchText(text);
      // Debounce search
      const timeoutId = setTimeout(() => {
        loadCustomers(true);
      }, 500);
      
      return () => clearTimeout(timeoutId);
    };

    const handleSelectCustomer = (customer: Customer) => {
      // Đóng bottom sheet trước
      hideBottomSheet(ref);

      // Sau đó gọi callback với một delay nhỏ để đảm bảo bottom sheet đã đóng hoàn toàn
      setTimeout(() => {
        onSelectCustomer(customer);
      }, 300); // 300ms để đảm bảo animation đóng bottom sheet hoàn tất
    };

    const renderCustomerItem = ({ item }: { item: Customer }) => (
      <TouchableOpacity
        style={styles.customerItem}
        onPress={() => handleSelectCustomer(item)}
        activeOpacity={0.7}
      >
        <View style={styles.avatarContainer}>
          {
            item.AvatarUrl ? (
              <Image
                source={{ uri: ConfigAPI.urlImg + item.AvatarUrl }}
                style={{ width: 40, height: 40, borderRadius: 20 }}
                resizeMode="cover"
              />
            ) : (
              <Winicon 
                src="fill/users/profile" 
                size={24} 
                color={ColorThemes.light.primary_main_color} 
              />
            )
          }
          
        </View>
        <View style={styles.customerInfo}>
          <Text style={styles.customerName}>{item.Name}</Text>
          {item.Mobile && (
            <Text style={styles.customerPhone}>{item.Mobile}</Text>
          )}
        </View>
        <Winicon 
          src="outline/arrows/chevron-right" 
          size={16} 
          color={ColorThemes.light.neutral_text_subtitle_color} 
        />
      </TouchableOpacity>
    );

    const renderFooter = () => {
      if (!loadingMore) return null;
      return (
        <View style={styles.loadingFooter}>
          <ActivityIndicator 
            size="small" 
            color={ColorThemes.light.primary_main_color} 
          />
        </View>
      );
    };

    const renderEmpty = () => {
      if (loading) return null;
      return (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>
            {searchText ? 'Không tìm thấy khách hàng' : 'Chưa có khách hàng nào'}
          </Text>
        </View>
      );
    };

    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Chọn người nhận</Text>
          <TouchableOpacity onPress={() => hideBottomSheet(ref)} >
            <Winicon 
              src="outline/user interface/e-remove" 
              size={20} 
              color={ColorThemes.light.neutral_text_subtitle_color} 
            />
          </TouchableOpacity>
        </View>

        <View style={styles.searchContainer}>
          <View style={styles.searchInputContainer}>
            <Winicon 
              src="outline/user interface/search" 
              size={16} 
              color={ColorThemes.light.neutral_text_subtitle_color} 
            />
            <TextInput
              style={styles.searchInput}
              placeholder="Tìm kiếm theo tên hoặc số điện thoại"
              value={searchText}
              onChangeText={handleSearch}
              placeholderTextColor={ColorThemes.light.neutral_text_subtitle_color}
            />
          </View>
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator 
              size="large" 
              color={ColorThemes.light.primary_main_color} 
            />
          </View>
        ) : (
          <FlatList
            data={customers}
            renderItem={renderCustomerItem}
            keyExtractor={(item) => item.Id}
            onEndReached={() => {
              if (hasMore && !loadingMore) {
                loadCustomers(false);
              }
            }}
            onEndReachedThreshold={0.5}
            ListFooterComponent={renderFooter}
            ListEmptyComponent={renderEmpty}
            showsVerticalScrollIndicator={false}
            style={styles.list}
          />
        )}
      </View>
    );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: ColorThemes.light.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
    minHeight: 600,
    width: '100%',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: ColorThemes.light.neutral_lighter_border_color,
  },
  title: {
    ...TypoSkin.heading6,
    color: ColorThemes.light.neutral_text_title_color,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: ColorThemes.light.neutral_main_background_color,
    borderRadius: 8,
    paddingHorizontal: 12,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 14,
    color: ColorThemes.light.neutral_text_title_color,
  },
  list: {
    flex: 1,
  },
  customerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  avatarContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: ColorThemes.light.primary_background,
    alignItems: 'center',
    justifyContent: 'center',
  },
  customerInfo: {
    flex: 1,
  },
  customerName: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '600',
  },
  customerPhone: {
    ...TypoSkin.regular3,
    color: ColorThemes.light.neutral_text_subtitle_color,
    marginTop: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingFooter: {
    paddingVertical: 16,
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_subtitle_color,
  },
});

export default CustomerBottomSheet;
