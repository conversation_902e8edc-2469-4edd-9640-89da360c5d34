import React, {useState} from 'react';
import {TouchableOpacity, Image, ImageStyle, ViewStyle} from 'react-native';
import ZoomableImageModal from './ZoomableImageModal';

interface ClickableImageProps {
  source: {uri: string} | any;
  style?: ImageStyle;
  containerStyle?: ViewStyle;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
}

const ClickableImage: React.FC<ClickableImageProps> = ({
  source,
  style,
  containerStyle,
  resizeMode = 'contain',
}) => {
  const [modalVisible, setModalVisible] = useState(false);

  const handleImagePress = () => {
    setModalVisible(true);
  };

  const handleCloseModal = () => {
    setModalVisible(false);
  };

  // Get the image URI for the modal
  const imageUri = typeof source === 'object' && source.uri ? source.uri : '';

  return (
    <>
      <TouchableOpacity
        style={containerStyle}
        onPress={handleImagePress}
        activeOpacity={0.8}>
        <Image source={source} style={style} resizeMode={resizeMode} />
      </TouchableOpacity>

      <ZoomableImageModal
        visible={modalVisible}
        imageUri={imageUri}
        onClose={handleCloseModal}
      />
    </>
  );
};

export default ClickableImage;
