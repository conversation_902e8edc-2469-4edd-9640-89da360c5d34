import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, ScrollView } from 'react-native';
import { AppButton, Winicon } from 'wini-mobile-components';
import { ColorThemes } from '../../../assets/skin/colors';
import { TypoSkin } from '../../../assets/skin/typography';
import store from '../../../redux/store/store';

interface Step2PasswordVerificationProps {
  onNext: (password: string, email: string) => void;
  isLoading: boolean;
}

const Step2PasswordVerification: React.FC<Step2PasswordVerificationProps> = ({
  onNext,
  isLoading,
}) => {
  const [password, setPassword] = useState('');
  const [Email, setEmail] = useState(store.getState().customer.data?.Email || '');
  const [showPassword, setShowPassword] = useState(false);

  const handleNext = () => {
    onNext(password, Email);
  };

  const isButtonEnabled = password.trim().length > 0 && !isLoading;

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.content}>
          {/* Description */}
          <View style={styles.descriptionContainer}>
            <Text style={styles.descriptionText}>
              Bạn có thể đổi địa chỉ email xác thực mới.
            </Text>
          </View>

          {/* Password Input */}
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Email</Text>
            <View style={styles.passwordInputContainer}>
              <TextInput
                style={styles.passwordInput}
                keyboardType='email-address'
                placeholder="Nhập Email của bạn"
                placeholderTextColor={ColorThemes.light.neutral_text_placeholder_color}
                value={Email}
                onChangeText={setEmail}
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>
            <Text style={styles.inputLabel}>Password</Text>
            <View style={styles.passwordInputContainer}>
              <TextInput
                style={styles.passwordInput}
                placeholder="Nhập mật khẩu của bạn"
                placeholderTextColor={ColorThemes.light.neutral_text_placeholder_color}
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
                autoCapitalize="none"
                autoCorrect={false}
              />
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => setShowPassword(!showPassword)}
              >
                <Winicon
                  src={showPassword ? 'outline/user interface/eye' : 'outline/accessibility/b-eye'}
                  size={20}
                  color={ColorThemes.light.neutral_text_subtitle_color}
                />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Button */}
      <View style={styles.buttonContainer}>
        <AppButton
          title="Tiếp theo"
          onPress={handleNext}
          disabled={!isButtonEnabled}
          backgroundColor={
            isButtonEnabled
              ? ColorThemes.light.primary_main_color
              : ColorThemes.light.neutral_lighter_border_color
          }
          containerStyle={styles.button}
          textStyle={{
            ...TypoSkin.buttonText1,
            fontWeight: '600',
            color: isButtonEnabled ? ColorThemes.light.white : ColorThemes.light.neutral_text_subtitle_color,
          }}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    paddingTop: 20,
  },
  descriptionContainer: {
    marginBottom: 32,
  },
  descriptionText: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_body_color,
    lineHeight: 20,
  },
  linkText: {
    color: ColorThemes.light.primary_main_color,
    fontWeight: '500',
  },
  inputContainer: {
    marginBottom: 24,
  },
  inputLabel: {
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_title_color,
    fontWeight: '500',
    marginBottom: 8,
  },
  passwordInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: ColorThemes.light.neutral_main_border_color,
    borderRadius: 8,
    backgroundColor: ColorThemes.light.neutral_absolute_background_color,
    marginBottom: 16,
  },
  passwordInput: {
    flex: 1,
    height: 48,
    paddingHorizontal: 16,
    ...TypoSkin.regular2,
    color: ColorThemes.light.neutral_text_title_color,
  },
  eyeButton: {
    padding: 12,
    marginRight: 4,
  },
  buttonContainer: {
    paddingBottom: 20,
  },
  button: {
    height: 48,
    borderRadius: 24,
    borderWidth: 0,
  },
  buttonText: {
    ...TypoSkin.buttonText1,
    fontWeight: '600',
    color: ColorThemes.light.white,    
  },
});

export default Step2PasswordVerification;
