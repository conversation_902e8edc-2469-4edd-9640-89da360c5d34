import React, {FC, useState} from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Image,
  ImageBackground,
} from 'react-native';
import {IconText} from '../components/TabEventComponents/components/IconText';
import {CountdownTimer} from '../components/TabEventComponents/components/CountdownTimer';
import {ColorThemes} from '../../../assets/skin/colors';
import {NewsEvent} from '../../../redux/models/newsEvent';
import {checkTimeWithNow, formatTimestamp} from '../../../utils/Utils';
import BasePopupConfirm from '../../../components/Popup/BasePopupConfirm';
import {useNavigation} from '@react-navigation/native';
import {RootScreen} from '../../../router/router';
import {eventRegisterAction} from '../../../redux/actions/eventRegisterAction';
import {useSelectorCustomerState} from '../../../redux/hook/customerHook';

interface EventCardProps {
  item: NewsEvent;
  onRegisterDone?: (id: string) => void;
}

const defaultImage = require('../../../assets/images/default_img.png');

export const EventCardHorizontal: FC<EventCardProps> = ({
  item,
  onRegisterDone,
}) => {
  const navigation = useNavigation<any>();
  const [showConfirm, setShowConfirm] = useState(false);
  const [loading, setLoading] = useState(false);
  const customerHook = useSelectorCustomerState();

  const onShowConfirm = () => {
    setShowConfirm(true);
  };

  const onCancel = () => {
    setShowConfirm(false);
  };

  const onConfirm = async () => {
    try {
      setLoading(true);
      const customerId = customerHook.data?.Id;
      const dataCreate = {
        Status: 'registered',
        NewsEventId: item.Id,
        CustomerId: customerId ?? '',
      };
      await eventRegisterAction.create(dataCreate);
      onRegisterDone?.(item.Id);
      setShowConfirm(false);
    } catch (error) {
      console.log('error', error);
    } finally {
      setLoading(false);
    }
  };

  const navigateToEventDetail = (event: NewsEvent) => {
    navigation.navigate(RootScreen.DetailPost, {
      item: event,
    });
  };

  return (
    <TouchableOpacity
      style={styles.hCard}
      onPress={() => navigateToEventDetail(item)}>
      {checkTimeWithNow(item.DateStart) ? (
        <ImageBackground
          source={item.Img ? {uri: item.Img} : defaultImage}
          style={styles.hCardImage}>
          <CountdownTimer TargetDate={item.DateStart} />
        </ImageBackground>
      ) : (
        <Image
          source={item.Img ? {uri: item.Img} : defaultImage}
          style={styles.hCardImage}
        />
      )}
      <View style={styles.hCardContent}>
        <Text style={[styles.hCardTitle]} numberOfLines={2}>
          {item.Title}
        </Text>
        <View style={styles.hCardFooter}>
          <Text
            style={[
              styles.hCardLocation,
              {width: item.isRegistered ? '50%' : '60%'},
            ]}
            numberOfLines={1}>
            {item.Address}
          </Text>
          {item.isRegistered ? (
            <View style={styles.registeredChip}>
              <View style={styles.checkIcon}>
                <Text style={styles.checkText}>✓</Text>
              </View>
              <Text style={styles.registeredText}>Đã đăng ký</Text>
            </View>
          ) : (
            <TouchableOpacity style={styles.joinButton} onPress={onShowConfirm}>
              <Text style={styles.joinButtonText}>Tham gia</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
      <BasePopupConfirm
        visible={showConfirm}
        loading={loading}
        title="Xác nhận đăng ký"
        message="Xác nhận đăng ký tham gia sự kiện này"
        onCancel={onCancel}
        onConfirm={onConfirm}
      />
    </TouchableOpacity>
  );
};

export const EventCardVertical: FC<EventCardProps> = ({item}) => {
  const navigation = useNavigation<any>();

  const navigateToEventDetail = (event: NewsEvent) => {
    navigation.navigate(RootScreen.DetailPost, {
      item: event,
    });
  };

  return (
    <TouchableOpacity
      style={styles.vCard}
      onPress={() => navigateToEventDetail(item)}>
      {checkTimeWithNow(item.DateStart) ? (
        <ImageBackground
          source={item.Img ? {uri: item.Img} : defaultImage}
          style={styles.vCardImage}>
          <CountdownTimer TargetDate={item.DateStart} />
        </ImageBackground>
      ) : (
        <Image
          source={item.Img ? {uri: item.Img} : defaultImage}
          style={styles.vCardImage}
        />
      )}
      <View style={styles.vCardContent}>
        <Text style={[styles.vCardTitle]}>{item.Title}</Text>
        <IconText
          icon={require('../../../assets/icons/pinmap.png')}
          text={item.Address}
        />
        <IconText
          icon={require('../../../assets/icons/calendar.png')}
          text={formatTimestamp(item.DateStart)}
        />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  hCard: {
    width: 280,
    backgroundColor: 'white',
    borderRadius: 12,
    marginRight: 16,
    elevation: 1,
  },
  hCardImage: {
    width: '100%',
    height: 140,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    borderRadius: 8,
  },
  hCardContent: {padding: 12},
  hCardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
    color: ColorThemes.light.infor_text_color,
  },
  hCardLocation: {
    fontSize: 14,
    flexDirection: 'row',
    justifyContent: 'space-between',
    color: ColorThemes.light.infor_text_color,
  },
  hCardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  joinButton: {
    height: 26,
    width: 100,
    backgroundColor: '#FFC043',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  joinButtonText: {
    color: ColorThemes.light.white,
    fontSize: 14,
  },
  registeredChip: {
    height: 26,
    backgroundColor: ColorThemes.light.secondary2_sub_color,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
  },
  checkIcon: {
    width: 16,
    height: 16,
    backgroundColor: ColorThemes.light.secondary6_darker_color,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 6,
  },
  checkText: {
    color: ColorThemes.light.white,
    fontSize: 8,
    fontWeight: 'bold',
  },
  registeredText: {
    color: ColorThemes.light.white,
    fontSize: 14,
    fontWeight: '500',
  },
  vCard: {
    height: 140,
    backgroundColor: 'white',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.08,
    shadowRadius: 3,
    elevation: 2,
    flexDirection: 'row',
    padding: 10,
  },
  vCardImage: {
    width: 140,
    height: 120,
    borderRadius: 8,
    marginRight: 12,
  },
  vCardContent: {flex: 1, justifyContent: 'center'},
  vCardTitle: {
    fontSize: 16,
    marginBottom: 8,
    color: ColorThemes.light.infor_text_color,
  },
});
