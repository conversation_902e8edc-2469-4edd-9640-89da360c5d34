import { DataController } from '../../base/baseController';
import { PaymentType, StatusOrder } from '../../Config/Contanst';

export class ShopDA {
  private ShopController: DataController;
  private OrderController: DataController;
  private PaymentWithdrawController: DataController;

  constructor() {
    this.ShopController = new DataController('Shop');
    this.OrderController = new DataController('Order');
    this.PaymentWithdrawController = new DataController('PaymentWithdraw');
  }

  async getShop(cusId: string) {
    const response = await this.ShopController.aggregateList({
      page: 1,
      size: 1,
      searchRaw: `@CustomerId: {${cusId}}`,
    });
    if (response?.code === 200) {
      return response;
    }
    return null;
  }

  /**
   * Lấy thông tin đơn hàng và giao dịch rút tiền theo shopId
   * @param shopId - ID của shop
   * @returns Object chứa tổng tiền đơn hàng, tổng tiền giao dịch và số dư
   */
  async getShopFinancialSummary(shopId: string) {
    try {
      // Bước 1: Lấy tất cả đơn hàng có status = 3 (thành công)
      const [orderResponse, paymentWithdrawResponse] = await Promise.all([
        this.OrderController.group({
          reducers: 'LOAD * GROUPBY 1 @ShopId REDUCE SUM 1 @Value AS TotalAmount',
          searchRaw: `@ShopId: {${shopId}} @Status: [${StatusOrder.success}] @PaymentType: [${PaymentType.vnPay}]`,
        }),
        this.PaymentWithdrawController.aggregateList({
          page: 1,
          size: 1000,
          searchRaw: `@ShopId: {${shopId}} @Status: [1 2]`,
        }),
      ]);
      // Tính tổng tiền đơn hàng
      let totalOrderAmount = 0;
      console.log("check-orderResponse", orderResponse);
      if (orderResponse?.code === 200 && orderResponse.data) {
        totalOrderAmount = orderResponse.data.reduce((sum: number, order: any) => {
          return sum + (order.TotalAmount || 0);
        }, 0);
      }

      // Tính tổng tiền giao dịch rút
      let totalWithdrawAmount = 0;

      if (paymentWithdrawResponse?.code === 200 && paymentWithdrawResponse.data && paymentWithdrawResponse.data.length > 0) {
        totalWithdrawAmount = paymentWithdrawResponse.data.reduce((sum: number, payment: any) => {
          return sum + (payment.TotalPrice || 0);
        }, 0);
        console.log("Final totalWithdrawAmount:", totalWithdrawAmount);
      } else {
        console.log("Condition not met - code:", paymentWithdrawResponse?.code, "data:", paymentWithdrawResponse?.data);
      }

      // Tính số dư (tổng đơn hàng - tổng rút tiền)
      const balance = totalOrderAmount - totalWithdrawAmount;
      console.log("check-balance", balance)

      return {
        code: 200,
        data: {
          shopId: shopId,
          totalOrderAmount: totalOrderAmount,
          totalWithdrawAmount: totalWithdrawAmount,
          balance: balance,
          orders: orderResponse?.data || [],
          withdrawals: paymentWithdrawResponse?.data || [],
        },
        message: 'Lấy thông tin tài chính thành công',
      };
    } catch (error) {
      console.error('getShopFinancialSummary error:', error);
      return {
        code: 500,
        data: null,
        message: 'Lỗi khi lấy thông tin tài chính',
      };
    }
  }
}

export default ShopDA;
